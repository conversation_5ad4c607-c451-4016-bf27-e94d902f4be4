import { create } from 'zustand';

// Mock data for users
const mockUsers = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'admin', avatar: null },
    { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'agent', avatar: null },
    { id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'agent', avatar: null },
    { id: '4', name: '<PERSON>', email: '<EMAIL>', role: 'agent', avatar: null },
    { id: '5', name: '<PERSON>', email: '<EMAIL>', role: 'agent', avatar: null },
];

// Mock data for tickets
const mockTickets = [
    {
        id: 'TK-001',
        title: 'Login issue with mobile app',
        description: 'Users are unable to login to the mobile application. The login button appears to be unresponsive after entering credentials.',
        status: 'open',
        priority: 'high',
        assigneeId: '2',
        reporterId: '1',
        category: 'Technical',
        tags: ['mobile', 'login', 'bug'],
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
        comments: [
            {
                id: 'c1',
                userId: '2',
                content: 'I\'ve reproduced the issue. Looking into the authentication service.',
                createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            }
        ]
    },
    {
        id: 'TK-002',
        title: 'Payment gateway not working',
        description: 'The payment gateway is returning errors when users try to make payments. Error code: PAYMENT_FAILED_001',
        status: 'in-progress',
        priority: 'critical',
        assigneeId: '3',
        reporterId: '1',
        category: 'Financial',
        tags: ['payment', 'gateway', 'critical'],
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
        comments: [
            {
                id: 'c2',
                userId: '3',
                content: 'Contacted the payment provider. They are investigating the issue.',
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            },
            {
                id: 'c3',
                userId: '1',
                content: 'This is affecting multiple customers. Please prioritize.',
                createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            }
        ]
    },
    {
        id: 'TK-003',
        title: 'Feature request: Dark mode',
        description: 'Multiple users have requested a dark mode option for the application to reduce eye strain during night usage.',
        status: 'open',
        priority: 'low',
        assigneeId: '4',
        reporterId: '2',
        category: 'Feature Request',
        tags: ['ui', 'feature', 'enhancement'],
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        comments: []
    },
    {
        id: 'TK-004',
        title: 'Database connection timeout',
        description: 'The application is experiencing intermittent database connection timeouts, causing slow response times.',
        status: 'resolved',
        priority: 'high',
        assigneeId: '5',
        reporterId: '1',
        category: 'Technical',
        tags: ['database', 'performance', 'timeout'],
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        dueDate: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago (resolved)
        comments: [
            {
                id: 'c4',
                userId: '5',
                content: 'Identified the issue with connection pooling. Implementing fix.',
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            },
            {
                id: 'c5',
                userId: '5',
                content: 'Fix deployed and tested. Connection timeouts resolved.',
                createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            }
        ]
    },
    {
        id: 'TK-005',
        title: 'Email notifications not sending',
        description: 'Users are not receiving email notifications for ticket updates and system alerts.',
        status: 'open',
        priority: 'medium',
        assigneeId: '2',
        reporterId: '3',
        category: 'Technical',
        tags: ['email', 'notifications', 'smtp'],
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        dueDate: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(), // 48 hours from now
        comments: []
    },
    {
        id: 'TK-006',
        title: 'User profile page loading slowly',
        description: 'The user profile page takes more than 10 seconds to load, affecting user experience.',
        status: 'in-progress',
        priority: 'medium',
        assigneeId: '4',
        reporterId: '2',
        category: 'Performance',
        tags: ['performance', 'ui', 'loading'],
        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
        comments: [
            {
                id: 'c6',
                userId: '4',
                content: 'Analyzing the database queries. Found some inefficient joins.',
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            }
        ]
    }
];

// Categories for tickets
const categories = [
    'Technical',
    'Feature Request',
    'Bug Report',
    'Performance',
    'Security',
    'Financial',
    'User Experience',
    'Integration',
    'Documentation'
];

// Priority levels
const priorities = ['low', 'medium', 'high', 'critical'];

// Status options
const statuses = ['open', 'in-progress', 'resolved', 'closed', 'on-hold'];

// Generate unique ticket ID
const generateTicketId = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `TK-${timestamp}`;
};

// Zustand store for ticket management
export const useTicketStore = create((set, get) => ({
    // State
    tickets: mockTickets,
    users: mockUsers,
    categories,
    priorities,
    statuses,
    filters: {
        status: 'all',
        priority: 'all',
        assignee: 'all',
        category: 'all',
        search: ''
    },
    selectedTicket: null,
    isLoading: false,

    // Actions
    setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
    })),

    setSelectedTicket: (ticket) => set({ selectedTicket: ticket }),

    createTicket: (ticketData) => set((state) => {
        const newTicket = {
            id: generateTicketId(),
            ...ticketData,
            status: 'open',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            comments: []
        };
        return {
            tickets: [newTicket, ...state.tickets]
        };
    }),

    updateTicket: (ticketId, updates) => set((state) => ({
        tickets: state.tickets.map(ticket =>
            ticket.id === ticketId
                ? { ...ticket, ...updates, updatedAt: new Date().toISOString() }
                : ticket
        )
    })),

    deleteTicket: (ticketId) => set((state) => ({
        tickets: state.tickets.filter(ticket => ticket.id !== ticketId),
        selectedTicket: state.selectedTicket?.id === ticketId ? null : state.selectedTicket
    })),

    addComment: (ticketId, comment) => set((state) => ({
        tickets: state.tickets.map(ticket =>
            ticket.id === ticketId
                ? {
                    ...ticket,
                    comments: [...ticket.comments, {
                        id: `c${Date.now()}`,
                        ...comment,
                        createdAt: new Date().toISOString()
                    }],
                    updatedAt: new Date().toISOString()
                }
                : ticket
        )
    })),

    // Computed values
    getFilteredTickets: () => {
        const { tickets, filters } = get();
        return tickets.filter(ticket => {
            const matchesStatus = filters.status === 'all' || ticket.status === filters.status;
            const matchesPriority = filters.priority === 'all' || ticket.priority === filters.priority;
            const matchesAssignee = filters.assignee === 'all' || ticket.assigneeId === filters.assignee;
            const matchesCategory = filters.category === 'all' || ticket.category === filters.category;
            const matchesSearch = filters.search === '' || 
                ticket.title.toLowerCase().includes(filters.search.toLowerCase()) ||
                ticket.description.toLowerCase().includes(filters.search.toLowerCase()) ||
                ticket.id.toLowerCase().includes(filters.search.toLowerCase());

            return matchesStatus && matchesPriority && matchesAssignee && matchesCategory && matchesSearch;
        });
    },

    getTicketById: (ticketId) => {
        const { tickets } = get();
        return tickets.find(ticket => ticket.id === ticketId);
    },

    getUserById: (userId) => {
        const { users } = get();
        return users.find(user => user.id === userId);
    },

    getTicketStats: () => {
        const { tickets } = get();
        return {
            total: tickets.length,
            open: tickets.filter(t => t.status === 'open').length,
            inProgress: tickets.filter(t => t.status === 'in-progress').length,
            resolved: tickets.filter(t => t.status === 'resolved').length,
            closed: tickets.filter(t => t.status === 'closed').length,
            critical: tickets.filter(t => t.priority === 'critical').length,
            high: tickets.filter(t => t.priority === 'high').length,
            overdue: tickets.filter(t => new Date(t.dueDate) < new Date() && !['resolved', 'closed'].includes(t.status)).length
        };
    }
}));
