import React, { useState, useEffect, useRef } from 'react';
import SIPModule from './Test';
import { RegistererState } from 'sip.js';

const SIPPhone = ({ config }) => {
    const audioRef = useRef(null);
    const remoteAudioRef = useRef(null);

    const [number, setNumber] = useState('');
    const [callStatus, setCallStatus] = useState('Ready to connect');
    const [isRegistered, setIsRegistered] = useState(false);
    const [isMuted, setIsMuted] = useState(false);
    const [isOnHold, setIsOnHold] = useState(false);
    const [incomingCall, setIncomingCall] = useState(null);
    const [activeCall, setActiveCall] = useState(null);

    const sipRef = useRef(new SIPModule({
        ...config,
        setNumberCallback: (num) => setNumber(num),
    }));

    useEffect(() => {
        const sip = sipRef.current;

        sip.setAudioElement(audioRef.current);
        sip.setMediaElement(remoteAudioRef.current);
        sip.start();

        const handleStateChange = (state) => {
            if (state === RegistererState.Registered) {
                setIsRegistered(true);
                setCallStatus('Registered and ready');
            } else if (state === RegistererState.Unregistered) {
                setIsRegistered(false);
                setCallStatus('Disconnected');
            }
        };

        sip.registerer?.stateChange.addListener(handleStateChange);

        return () => {
            sip.stop();
            sip.registerer?.stateChange.removeListener(handleStateChange);
        };
    }, [config]);

    useEffect(() => {
        const sip = sipRef.current;

        const handleIncomingCall = (invitation) => {
            const caller = invitation.remoteIdentity.uri.user;
            setIncomingCall(caller);
            setCallStatus(`Incoming call from ${caller}`);
        };

        sip.onInvite = handleIncomingCall;

        return () => {
            sip.onInvite = () => { };
        };
    }, []);

    const handleRegister = () => {
        try {
            sipRef.current.registerSIP();
            setCallStatus('Registering...');
        } catch (error) {
            console.error('Registration error:', error);
            setCallStatus('Registration failed');
        }
    };

    const handleUnregister = () => {
        try {
            sipRef.current.unregisterSIP();
            setCallStatus('Unregistering...');
        } catch (error) {
            console.error('Unregistration error:', error);
            setCallStatus('Unregistration failed');
        }
    };

    const handleCall = () => {
        if (number && isRegistered) {
            try {
                sipRef.current.onMakeCall(number);
                setActiveCall(number);
                setCallStatus(`Calling ${number}`);
            } catch (error) {
                console.error('Call error:', error);
                setCallStatus('Call failed');
            }
        }
    };

    const handleEndCall = () => {
        try {
            sipRef.current.onEndCall();
            setActiveCall(null);
            setIncomingCall(null);
            setCallStatus(isRegistered ? 'Ready for calls' : 'Disconnected');
        } catch (error) {
            console.error('End call error:', error);
            setCallStatus('Failed to end call');
        }
    };

    const handleAcceptCall = () => {
        try {
            sipRef.current.onCallAccept();
            setActiveCall(incomingCall);
            setIncomingCall(null);
            setCallStatus(`In call with ${incomingCall}`);
        } catch (error) {
            console.error('Answer error:', error);
            setCallStatus('Failed to answer call');
        }
    };

    const handleRejectCall = () => {
        try {
            sipRef.current.onEndCall();
            setIncomingCall(null);
            setCallStatus(isRegistered ? 'Ready for calls' : 'Disconnected');
        } catch (error) {
            console.error('Reject error:', error);
            setCallStatus('Failed to reject call');
        }
    };

    const toggleMute = () => {
        try {
            if (isMuted) {
                sipRef.current.onUnmute();
            } else {
                sipRef.current.onMute();
            }
            setIsMuted(!isMuted);
        } catch (error) {
            console.error('Mute error:', error);
        }
    };

    const toggleHold = () => {
        try {
            if (isOnHold) {
                sipRef.current.onUnhold();
            } else {
                sipRef.current.onHold();
            }
            setIsOnHold(!isOnHold);
        } catch (error) {
            console.error('Hold error:', error);
        }
    };

    const handleDTMF = (digit) => {
        try {
            sipRef.current.onDTMFInput(digit);
        } catch (error) {
            console.error('DTMF error:', error);
        }
    };

    const keypadButtons = [
        '1', '2', '3',
        '4', '5', '6',
        '7', '8', '9',
        '*', '0', '#'
    ];

    return (
        <div className="sip-phone">
            <h2>SIP Phone</h2>

            <div className="status-display">
                <p>Status: {callStatus}</p>
                {activeCall && <p>Active call: {activeCall}</p>}
                {incomingCall && <p>Incoming call: {incomingCall}</p>}
            </div>

            <div className="registration-controls">
                {!isRegistered ? (
                    <button onClick={handleRegister} disabled={callStatus === 'Registering...'}>
                        {callStatus === 'Registering...' ? 'Registering...' : 'Register'}
                    </button>
                ) : (
                    <button onClick={handleUnregister} disabled={callStatus === 'Unregistering...'}>
                        {callStatus === 'Unregistering...' ? 'Unregistering...' : 'Unregister'}
                    </button>
                )}
            </div>

            <div className="call-controls">
                <input
                    type="text"
                    value={number}
                    onChange={(e) => setNumber(e.target.value)}
                    placeholder="Enter number"
                    disabled={!!activeCall || !!incomingCall}
                />

                {!activeCall && !incomingCall ? (
                    <button onClick={handleCall} disabled={!isRegistered || !number}>
                        Call
                    </button>
                ) : (
                    <button onClick={handleEndCall}>End Call</button>
                )}

                {incomingCall && (
                    <div className="incoming-call">
                        <button onClick={handleAcceptCall}>Answer</button>
                        <button onClick={handleRejectCall}>Reject</button>
                    </div>
                )}
            </div>

            <div className="call-features">
                <button onClick={toggleMute} disabled={!activeCall} className={isMuted ? 'active' : ''}>
                    {isMuted ? 'Unmute' : 'Mute'}
                </button>
                <button onClick={toggleHold} disabled={!activeCall} className={isOnHold ? 'active' : ''}>
                    {isOnHold ? 'Unhold' : 'Hold'}
                </button>
            </div>

            <div className="keypad">
                {keypadButtons.map((btn) => (
                    <button key={btn} onClick={() => handleDTMF(btn)} disabled={!activeCall}>
                        {btn}
                    </button>
                ))}
            </div>

            <audio ref={audioRef} loop src="/telephone_ring.mp3" style={{ display: 'none' }} />
            <audio ref={remoteAudioRef} autoPlay controls style={{ width: '100%' }} />
        </div>
    );
};

export default SIPPhone;
