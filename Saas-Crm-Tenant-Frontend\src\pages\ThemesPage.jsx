import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
    ArrowLeft, 
    Palette, 
    Save, 
    RotateCcw, 
    Eye,
    Download,
    Upload,
    Paintbrush,
    Type,
    Layout
} from 'lucide-react';

export default function ThemesPage() {
    const navigate = useNavigate();
    
    const [theme, setTheme] = useState({
        colors: {
            primary: '#3b82f6',
            secondary: '#64748b',
            accent: '#10b981',
            background: '#ffffff',
            surface: '#f8fafc',
            text: '#1e293b',
            textSecondary: '#64748b',
            border: '#e2e8f0',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444',
            info: '#3b82f6'
        },
        typography: {
            fontFamily: 'Inter, system-ui, sans-serif',
            fontSize: {
                xs: '0.75rem',
                sm: '0.875rem',
                base: '1rem',
                lg: '1.125rem',
                xl: '1.25rem',
                '2xl': '1.5rem',
                '3xl': '1.875rem'
            },
            fontWeight: {
                normal: '400',
                medium: '500',
                semibold: '600',
                bold: '700'
            }
        },
        layout: {
            borderRadius: '0.5rem',
            spacing: {
                xs: '0.25rem',
                sm: '0.5rem',
                md: '1rem',
                lg: '1.5rem',
                xl: '2rem'
            },
            shadows: {
                sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)'
            }
        }
    });

    const [previewMode, setPreviewMode] = useState(false);

    const handleColorChange = (colorKey, value) => {
        setTheme(prev => ({
            ...prev,
            colors: {
                ...prev.colors,
                [colorKey]: value
            }
        }));
    };

    const handleTypographyChange = (category, key, value) => {
        setTheme(prev => ({
            ...prev,
            typography: {
                ...prev.typography,
                [category]: typeof prev.typography[category] === 'object' 
                    ? { ...prev.typography[category], [key]: value }
                    : value
            }
        }));
    };

    const handleLayoutChange = (category, key, value) => {
        setTheme(prev => ({
            ...prev,
            layout: {
                ...prev.layout,
                [category]: typeof prev.layout[category] === 'object' 
                    ? { ...prev.layout[category], [key]: value }
                    : value
            }
        }));
    };

    const resetToDefault = () => {
        // Reset to default theme
        setTheme({
            colors: {
                primary: '#3b82f6',
                secondary: '#64748b',
                accent: '#10b981',
                background: '#ffffff',
                surface: '#f8fafc',
                text: '#1e293b',
                textSecondary: '#64748b',
                border: '#e2e8f0',
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444',
                info: '#3b82f6'
            },
            typography: {
                fontFamily: 'Inter, system-ui, sans-serif',
                fontSize: {
                    xs: '0.75rem',
                    sm: '0.875rem',
                    base: '1rem',
                    lg: '1.125rem',
                    xl: '1.25rem',
                    '2xl': '1.5rem',
                    '3xl': '1.875rem'
                },
                fontWeight: {
                    normal: '400',
                    medium: '500',
                    semibold: '600',
                    bold: '700'
                }
            },
            layout: {
                borderRadius: '0.5rem',
                spacing: {
                    xs: '0.25rem',
                    sm: '0.5rem',
                    md: '1rem',
                    lg: '1.5rem',
                    xl: '2rem'
                },
                shadows: {
                    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)'
                }
            }
        });
    };

    const saveTheme = () => {
        localStorage.setItem('accordTheme', JSON.stringify(theme));
        alert('Theme saved successfully!');
    };

    const exportTheme = () => {
        const dataStr = JSON.stringify(theme, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
        
        const exportFileDefaultName = 'accord-theme.json';
        
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    };

    const importTheme = (event) => {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importedTheme = JSON.parse(e.target.result);
                    setTheme(importedTheme);
                    alert('Theme imported successfully!');
                } catch (error) {
                    alert('Error importing theme. Please check the file format.');
                }
            };
            reader.readAsText(file);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-4">
                        <div className="flex items-center gap-4">
                            <Button
                                variant="ghost"
                                onClick={() => navigate('/admin')}
                                className="flex items-center gap-2"
                            >
                                <ArrowLeft className="h-4 w-4" />
                                Back to Admin
                            </Button>
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                                    <Palette className="h-6 w-6" />
                                    Theme Configuration
                                </h1>
                                <p className="text-sm text-gray-600">Customize the appearance of your Accord instance</p>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                onClick={() => setPreviewMode(!previewMode)}
                                className="flex items-center gap-2"
                            >
                                <Eye className="h-4 w-4" />
                                {previewMode ? 'Edit' : 'Preview'}
                            </Button>
                            <Button
                                variant="outline"
                                onClick={resetToDefault}
                                className="flex items-center gap-2"
                            >
                                <RotateCcw className="h-4 w-4" />
                                Reset
                            </Button>
                            <Button
                                onClick={saveTheme}
                                className="flex items-center gap-2"
                            >
                                <Save className="h-4 w-4" />
                                Save Theme
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {!previewMode ? (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Theme Editor */}
                        <div className="lg:col-span-2">
                            <Tabs defaultValue="colors" className="space-y-6">
                                <TabsList className="grid w-full grid-cols-3">
                                    <TabsTrigger value="colors" className="flex items-center gap-2">
                                        <Paintbrush className="h-4 w-4" />
                                        Colors
                                    </TabsTrigger>
                                    <TabsTrigger value="typography" className="flex items-center gap-2">
                                        <Type className="h-4 w-4" />
                                        Typography
                                    </TabsTrigger>
                                    <TabsTrigger value="layout" className="flex items-center gap-2">
                                        <Layout className="h-4 w-4" />
                                        Layout
                                    </TabsTrigger>
                                </TabsList>

                                <TabsContent value="colors" className="space-y-6">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Color Palette</CardTitle>
                                            <CardDescription>
                                                Customize the color scheme for your application
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                {Object.entries(theme.colors).map(([key, value]) => (
                                                    <div key={key} className="space-y-2">
                                                        <Label htmlFor={key} className="capitalize">
                                                            {key.replace(/([A-Z])/g, ' $1').trim()}
                                                        </Label>
                                                        <div className="flex items-center gap-2">
                                                            <Input
                                                                id={key}
                                                                type="color"
                                                                value={value}
                                                                onChange={(e) => handleColorChange(key, e.target.value)}
                                                                className="w-16 h-10 p-1 border rounded"
                                                            />
                                                            <Input
                                                                type="text"
                                                                value={value}
                                                                onChange={(e) => handleColorChange(key, e.target.value)}
                                                                className="flex-1"
                                                            />
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                <TabsContent value="typography" className="space-y-6">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Typography Settings</CardTitle>
                                            <CardDescription>
                                                Configure fonts and text styling
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="fontFamily">Font Family</Label>
                                                <Input
                                                    id="fontFamily"
                                                    value={theme.typography.fontFamily}
                                                    onChange={(e) => handleTypographyChange('fontFamily', null, e.target.value)}
                                                />
                                            </div>
                                            
                                            <div className="space-y-4">
                                                <h4 className="font-medium">Font Sizes</h4>
                                                <div className="grid grid-cols-2 gap-4">
                                                    {Object.entries(theme.typography.fontSize).map(([key, value]) => (
                                                        <div key={key} className="space-y-2">
                                                            <Label htmlFor={`fontSize-${key}`} className="capitalize">
                                                                {key}
                                                            </Label>
                                                            <Input
                                                                id={`fontSize-${key}`}
                                                                value={value}
                                                                onChange={(e) => handleTypographyChange('fontSize', key, e.target.value)}
                                                            />
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                <TabsContent value="layout" className="space-y-6">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Layout Settings</CardTitle>
                                            <CardDescription>
                                                Configure spacing, borders, and shadows
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="borderRadius">Border Radius</Label>
                                                <Input
                                                    id="borderRadius"
                                                    value={theme.layout.borderRadius}
                                                    onChange={(e) => handleLayoutChange('borderRadius', null, e.target.value)}
                                                />
                                            </div>
                                            
                                            <div className="space-y-4">
                                                <h4 className="font-medium">Spacing</h4>
                                                <div className="grid grid-cols-2 gap-4">
                                                    {Object.entries(theme.layout.spacing).map(([key, value]) => (
                                                        <div key={key} className="space-y-2">
                                                            <Label htmlFor={`spacing-${key}`} className="capitalize">
                                                                {key}
                                                            </Label>
                                                            <Input
                                                                id={`spacing-${key}`}
                                                                value={value}
                                                                onChange={(e) => handleLayoutChange('spacing', key, e.target.value)}
                                                            />
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>
                            </Tabs>
                        </div>

                        {/* Preview Panel */}
                        <div className="lg:col-span-1">
                            <Card className="sticky top-8">
                                <CardHeader>
                                    <CardTitle>Live Preview</CardTitle>
                                    <CardDescription>
                                        See how your theme looks in real-time
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div 
                                        className="space-y-4 p-4 border rounded-lg"
                                        style={{
                                            backgroundColor: theme.colors.background,
                                            color: theme.colors.text,
                                            fontFamily: theme.typography.fontFamily,
                                            borderRadius: theme.layout.borderRadius
                                        }}
                                    >
                                        <div 
                                            className="p-3 rounded"
                                            style={{
                                                backgroundColor: theme.colors.primary,
                                                color: 'white',
                                                borderRadius: theme.layout.borderRadius
                                            }}
                                        >
                                            Primary Button
                                        </div>
                                        <div 
                                            className="p-3 rounded border"
                                            style={{
                                                backgroundColor: theme.colors.surface,
                                                borderColor: theme.colors.border,
                                                borderRadius: theme.layout.borderRadius
                                            }}
                                        >
                                            <h4 style={{ color: theme.colors.text, fontSize: theme.typography.fontSize.lg }}>
                                                Card Title
                                            </h4>
                                            <p style={{ color: theme.colors.textSecondary, fontSize: theme.typography.fontSize.sm }}>
                                                This is a preview of how your theme will look.
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Import/Export */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>Import/Export</CardTitle>
                                    <CardDescription>
                                        Save or load theme configurations
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <Button
                                        variant="outline"
                                        onClick={exportTheme}
                                        className="w-full flex items-center gap-2"
                                    >
                                        <Download className="h-4 w-4" />
                                        Export Theme
                                    </Button>
                                    <div>
                                        <input
                                            type="file"
                                            accept=".json"
                                            onChange={importTheme}
                                            className="hidden"
                                            id="import-theme"
                                        />
                                        <Button
                                            variant="outline"
                                            onClick={() => document.getElementById('import-theme').click()}
                                            className="w-full flex items-center gap-2"
                                        >
                                            <Upload className="h-4 w-4" />
                                            Import Theme
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Theme Preview</CardTitle>
                                <CardDescription>
                                    Full preview of your theme applied to sample components
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-gray-600">Full preview mode coming soon...</p>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </div>
    );
}
