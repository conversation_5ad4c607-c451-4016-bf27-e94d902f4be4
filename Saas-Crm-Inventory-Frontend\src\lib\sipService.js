
import {
    Invitation,
    Inviter,
    Registerer,
    RegistererState,
    SessionState,
    TransportState,
    UserAgent,
    Web,
} from "sip.js";


class SIPModule {
    constructor({
        sipDomain,
        name,
        authUser,
        authPass,
        wssPort,
        queues,
        setNumberCallback
    }) {
        this.sipDomain = sipDomain;
        this.name = name;
        this.authUser = authUser;
        this.authPass = authPass;
        this.wssPort = wssPort;
        this.queues = queues;
        this.setNumberCallback = setNumberCallback;

        this.uri = UserAgent.makeURI(`sip:${authUser}@${sipDomain}`);

        this._transferredSession = null;
        this._session = null;
        this._connected = false;
        this._registered = false;

        this.userAgent = null;
        this.registerer = null;
        this.isModalVisible = false;
        this.dialedNumber = "";
        this.incoming = false;
        this.outgoing = false;
        this.isConnected = false;
        this.isHold = false;
        this.isMute = false;
        this.sessionState = null;
        this.isTransferHold = false;
        this.isTransferMute = false;
        this.isTransferConnected = false;
        this.isBridged = false;
        this.incomingCallAnswered = false;
        this.callAnswered = false;

        this.mediaElement = null;
        this.audioElement = null;
        this.onInvite = this.onInvite.bind(this);
        this.onConnect = this.onConnect.bind(this);
        this.onDisconnect = this.onDisconnect.bind(this);
        this.onRegister = this.onRegister.bind(this);
        this.sessionListener = this.sessionListener.bind(this);
        this.incomingSessionListener = this.incomingSessionListener.bind(this);
        this.registerListener = this.registerListener.bind(this);
    }

    initialize() {
        this.userAgent = new UserAgent({
            authorizationUsername: this.authUser,
            authorizationPassword: this.authPass,
            transportOptions: {
                server: `wss://${this.sipDomain}:${this.wssPort}/ws`,
                connectionTimeout: 15,
                keepAliveInterval: 30,
                keepAliveDebounce: 10,
                wsServers: [`wss://${this.sipDomain}:${this.wssPort}/ws`],
            },
            uri: this.uri,
            logLevel: "debug",
            delegate: {
                onConnect: this.onConnect,
                onDisconnect: this.onDisconnect,
                onRegister: this.onRegister,
                onInvite: this.onInvite,
            },
            contactParams: {
                transport: "wss"
            },
            sessionDescriptionHandlerFactoryOptions: {
                peerConnectionConfiguration: {
                    iceServers: [
                        { urls: "stun:stun.l.google.com:19302" },
                        { urls: "stun:stun1.l.google.com:19302" }
                    ],
                    iceTransportPolicy: "all",
                    rtcpMuxPolicy: "require",
                    bundlePolicy: "max-bundle",
                    sdpSemantics: "unified-plan"
                },
                iceRestartEnabled: true
            }
        });

        this.registerer = new Registerer(this.userAgent);
    }

    start() {
        this.initialize();
    }

    stop() {
        if (this.registerer) {
            this.registerer.stateChange.removeListener(this.registerListener);
        }

        if (this._session) {
            this._session.stateChange.removeListener(this.sessionListener);
        }

        if (this.userAgent) {
            this.userAgent.stop();
        }
    }

    setError(error) {
        console.error("SIP Error:", error);
    }

    setNotification(message) {
        console.log("SIP Notification:", message);
    }

    playRinger() {
        if (this.audioElement && typeof this.audioElement.play === 'function') {
            try {
                this.audioElement.play()
                    .catch(e => console.error("Error playing ringtone:", e));
            } catch (error) {
                console.warn('Error playing ringtone:', error);
            }
        }
    }

    stopRing() {
        if (this.audioElement && typeof this.audioElement.pause === 'function') {
            try {
                this.audioElement.pause();
                this.audioElement.currentTime = 0;
            } catch (error) {
                console.warn('Error stopping ringtone:', error);
            }
        }
    }

    onInvite(invitation) {
        if (!this._session) {
            this.playRinger();

            this._session = invitation;
            this.isModalVisible = true;
            this.registerIncomingEvents();

            const dialedNumber = invitation.remoteIdentity.uri.user;
            this.dialedNumber = dialedNumber;
            this.incoming = true;

            if (this.setNumberCallback) {
                this.setNumberCallback(dialedNumber);
            }
        } else {
            invitation
                .reject()
                .then(() => {
                    this.setNotification(`Incoming call from ${invitation.remoteIdentity.uri.user} rejected due to client busy.`);
                })
                .catch((e) => this.setError(e));
        }
    }

    onTransferHold() {
        if (!this._transferredSession) return;

        const options = {
            sessionDescriptionHandlerModifiers: [Web.holdModifier],
        };

        this._transferredSession
            .invite(options)
            .then(() => this.isTransferHold = true)
            .catch((err) => this.setError(err.message));
    }

    onTransferUnhold() {
        if (!this._transferredSession) return;

        const options = {
            sessionDescriptionHandlerModifiers: [],
        };

        this._transferredSession
            .invite(options)
            .then(() => this.isTransferHold = false)
            .catch((err) => this.setError(err.message));
    }

    onTransferMute() {
        if (!this._transferredSession) return;

        const pc = this._transferredSession.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = false;
                this.isTransferMute = true;
            }
        });
    }

    onTransferUnmute() {
        if (!this._transferredSession) return;

        const pc = this._transferredSession.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = true;
                this.isTransferMute = false;
            }
        });
    }

    onHold() {
        if (!this._session) return;

        const options = {
            sessionDescriptionHandlerModifiers: [Web.holdModifier],
        };

        return this._session
            .invite(options)
            .then(() => this.isHold = true)
            .catch((err) => this.setError(err.message));
    }

    onUnhold() {
        if (!this._session) return;

        const options = {
            sessionDescriptionHandlerModifiers: [],
        };

        return this._session
            .invite(options)
            .then(() => this.isHold = false)
            .catch((err) => this.setError(err.message));
    }

    onMute() {
        if (!this._session) return;

        const pc = this._session.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = false;
                this.isMute = true;
            }
        });
    }

    onUnmute() {
        if (!this._session) return;

        const pc = this._session.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = true;
                this.isMute = false;
            }
        });
    }

    onBlindTransfer(target) {
        if (!this._session) return;

        const transferTarget = UserAgent.makeURI(`sip:${target}@${this.sipDomain}`);
        if (!transferTarget) {
            throw new Error("Failed to create transfer target URI.");
        }

        this._session
            .refer(transferTarget)
            .then(() => {
                this.setNotification("Call has been transferred");
                this.onEndCall();
            })
            .catch((error) => this.setError(error.message));
    }

    onTransferHangup() {
        if (!this._transferredSession) return;

        switch (this._transferredSession.state) {
            case SessionState.Initial:
            case SessionState.Establishing:
                if (this._transferredSession instanceof Inviter) {
                    this._transferredSession
                        .cancel()
                        .then((res) => this.setNotification(res));
                } else {
                    this._transferredSession.reject();
                }
                this._transferredSession.stateChange.removeListener(this.sessionListener);
                this._transferredSession = null;
                this.isTransferMute = false;
                this.isTransferHold = false;
                this.isTransferConnected = false;
                this.isBridged = false;
                break;

            case SessionState.Established:
                this._transferredSession.bye();
                this._transferredSession.stateChange.removeListener(this.sessionListener);
                this._transferredSession = null;
                this.isTransferMute = false;
                this.isTransferHold = false;
                this.isTransferConnected = false;
                this.isBridged = false;
                this.attachMedia();
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                break;

            default:
                break;
        }
    }

    onAttendedTransfer(number) {
        if (!this._session) return;

        const target = UserAgent.makeURI(`sip:${number}@${this.sipDomain}`);
        if (!target) {
            this.setError("Failed to create transfer target URI.");
            return;
        }

        const transferSession = new Inviter(this.userAgent, target, {
            earlyMedia: true,
        });

        const constraints = {
            audio: true,
            video: false,
        };

        const options = {
            earlyMedia: true,
            sessionDescriptionHandlerOptions: {
                constraints,
            },
        };

        transferSession.stateChange.addListener((state) => {
            switch (state) {
                case SessionState.Initial:
                    break;

                case SessionState.Establishing:
                    this.isTransferConnected = true;
                    const remoteStream = new MediaStream();
                    transferSession.sessionDescriptionHandler.peerConnection
                        .getReceivers()
                        .forEach((receiver) => {
                            if (receiver.track) {
                                remoteStream.addTrack(receiver.track);
                            }
                        });

                    if (this.mediaElement?.current) {
                        this.mediaElement.current.srcObject = remoteStream;
                        this.mediaElement.current.play().catch(e => console.error("Error playing media:", e));
                    }
                    break;

                case SessionState.Established:
                    break;

                case SessionState.Terminating:
                case SessionState.Terminated:
                    this.isTransferConnected = false;
                    this._transferredSession = null;
                    break;

                default:
                    throw new Error("Unknown session state.");
            }
        });

        transferSession.invite(options).catch((e) => this.setError(e));
        this._transferredSession = transferSession;
    }

    onAcceptTransfer() {
        if (!this._session || !this._transferredSession) return;

        const receivedTracks = [];
        const sessionA = this._session;
        const sessionB = this._transferredSession;

        sessionA.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                receivedTracks.push(receiver.track);
            });

        sessionB.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                receivedTracks.push(receiver.track);
            });

        const context = new AudioContext();
        const mediaStream = new MediaStream();

        const sessions = [sessionA, sessionB];
        sessions.forEach((session) => {
            const mixedOutput = context.createMediaStreamDestination();
            session.sessionDescriptionHandler.peerConnection
                .getReceivers()
                .forEach((receiver) => {
                    receivedTracks.forEach((track) => {
                        mediaStream.addTrack(receiver.track);
                        if (receiver.track.id !== track.id) {
                            const sourceStream = context.createMediaStreamSource(
                                new MediaStream([track])
                            );
                            sourceStream.connect(mixedOutput);
                        }
                    });
                });

            session.sessionDescriptionHandler.peerConnection
                .getSenders()
                .forEach((sender) => {
                    const sourceStream = context.createMediaStreamSource(
                        new MediaStream([sender.track])
                    );
                    sourceStream.connect(mixedOutput);
                });

            session.sessionDescriptionHandler.peerConnection
                .getSenders()[0]
                .replaceTrack(mixedOutput.stream.getTracks()[0])
                .then(() => {
                    this.isBridged = true;
                    if (this.isHold) this.onUnhold();
                })
                .catch((e) => this.setError(e));
        });

        if (this.mediaElement?.current) {
            this.mediaElement.current.srcObject = mediaStream;
            this.mediaElement.current.play().catch(e => console.error("Error playing media:", e));
        }
    }

    onConnect() {
        this._connected = true;
        this.setNotification("Connected to SIP server");
    }

    onDisconnect(error) {
        this._connected = false;
        this.setError(`Disconnected from SIP server: ${error}`);
    }

    onRegister() {
        this.setNotification("Registration successful");
    }

    sessionListener(state) {
        this.sessionState = state;
        this.setNotification(`Session state changed to ${state}`);

        switch (state) {
            case SessionState.Initial:
                break;

            case SessionState.Establishing:
                this.attachMedia();
                break;

            case SessionState.Established:
                this.stopRing();
                this.isConnected = true;
                this.callAnswered = true;
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                this.stopRing();
                this.cleanupMedia();
                this.incoming = false;
                this.isConnected = false;
                this.callAnswered = false;
                this._session = null;
                this.isModalVisible = false;
                this.isHold = false;
                break;

            default:
                throw new Error("Unknown session state.");
        }
    }

    incomingSessionListener(state) {
        this.sessionState = state;
        this.setNotification(`Incoming session state changed to ${state}`);

        switch (state) {
            case SessionState.Initial:
                break;

            case SessionState.Establishing:
                break;

            case SessionState.Established:
                this.attachMedia();
                this.stopRing();
                this.isConnected = true;
                this.callAnswered = true;
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                this.stopRing();
                this.cleanupMedia();
                this.incoming = false;
                this.isConnected = false;
                this.callAnswered = false;
                this._session = null;
                this.isModalVisible = false;
                this.isHold = false;
                break;

            default:
                throw new Error("Unknown session state.");
        }
    }

    registerListener(state) {
        switch (state) {
            case RegistererState.Registered:
                this._registered = true;
                this.setNotification("Successfully registered");
                break;

            case RegistererState.Unregistered:
            case RegistererState.Terminated:
                this._registered = false;
                this.setNotification("Unregistered");
                break;

            case RegistererState.Initial:
            default:
                break;
        }
    }

    registerEvents() {
        if (this._session) {
            this._session.stateChange.addListener(this.sessionListener);
        }
    }

    registerIncomingEvents() {
        if (this._session) {
            this._session.stateChange.addListener(this.incomingSessionListener);
        }
    }

    getSession() {
        return this._session;
    }

    getSessionState() {
        return this._session?.state;
    }

    isConnected() {
        return this._connected;
    }

    isRegistered() {
        return this._registered;
    }

    registerSIP() {
        console.log("Starting SIP user agent...");

        this.userAgent
            .start()
            .then(() => {
                this.userAgent.transport.stateChange.addListener((state) => {
                    console.log(`SIP transport state changed to: ${state}`);

                    switch (state) {
                        case TransportState.Disconnected:
                            this.setNotification("Transport disconnected");
                            break;
                        case TransportState.Connected:
                            this.setNotification("Transport connected");
                            break;
                        case TransportState.Connecting:
                            this.setNotification("Transport connecting...");
                            break;
                        default:
                            this.setNotification(`Transport state: ${state}`);
                            break;
                    }
                });

                const attemptRegistration = (retryCount = 0, maxRetries = 3) => {
                    console.log(`Attempting SIP registration (attempt ${retryCount + 1}/${maxRetries + 1})...`);

                    this.registerer
                        .register({
                            requestDelegate: {
                                onReject: (response) => {
                                    console.error(`Registration rejected: ${response.message.reasonPhrase}`);
                                    this.setError(`Registration rejected: ${response.message.reasonPhrase}`);
                                }
                            }
                        })
                        .then(() => {
                            console.log("SIP registration successful");
                            this.registerer.stateChange.addListener(this.registerListener);
                        })
                        .catch((error) => {
                            console.error(`SIP registration error: ${error.message}`);
                            this.setError(error.message);

                            if (retryCount < maxRetries) {
                                console.log(`Retrying registration in ${(retryCount + 1) * 2} seconds...`);
                                setTimeout(() => attemptRegistration(retryCount + 1, maxRetries), (retryCount + 1) * 2000);
                            }
                        });
                };
                attemptRegistration();
            })
            .catch((error) => {
                console.error(`SIP user agent start error: ${error.message}`);
                this.setError(error.message);
            });
    }

    unregisterSIP() {
        this.registerer
            .unregister()
            .then(() => this.setNotification("Successfully unregistered"))
            .catch((error) => this.setError(error.message));
    }

    onMakeCall(number) {
        const target = UserAgent.makeURI(`sip:${number}@${this.sipDomain}`);
        if (!target) {
            this.setError("Failed to create target URI.");
            return;
        }

        const inviter = new Inviter(this.userAgent, target, {
            earlyMedia: true,
        });

        const constraints = {
            audio: true,
            video: false,
        };

        const options = {
            earlyMedia: true,
            sessionDescriptionHandlerOptions: {
                constraints,
                iceGatheringTimeout: 5000,
                iceRestartEnabled: true,
            },
            rel100: "supported",
        };

        inviter.stateChange.addListener((state) => {
            this.setNotification(`Call state changed to: ${state}`);

            console.log(`Call state changed to: ${state}`);

            if (state === SessionState.Establishing) {
                this.setNotification(`Establishing call to ${number}...`);
            }
        });

        inviter
            .invite(options)
            .then(() => {
                this.setNotification(`Calling ${number}`);
                this._session = inviter;
                this.registerEvents();

                this.attachMedia();
            })
            .catch((error) => {
                this.setError(`Call failed: ${error.message}`);
                console.error("Call error details:", error);
            });
    }

    onDTMFInput(number) {
        if (!this._session) return;

        const options = {
            requestOptions: {
                body: {
                    contentDisposition: "render",
                    contentType: "application/dtmf-relay",
                    content: `Signal=${number}\r\nDuration=1000`,
                },
            },
        };

        this._session.info(options).catch(e => this.setError(e));
    }

    onEndCall() {
        this.stopRing();
        if (!this._session) return;

        switch (this._session.state) {
            case SessionState.Initial:
            case SessionState.Establishing:
                if (this._session instanceof Inviter) {
                    this._session.cancel().then(() => this.setNotification("Call cancelled"));
                } else {
                    this._session.reject();
                }
                this._session = null;
                this.isModalVisible = false;
                break;

            case SessionState.Established:
                this._session.bye().then(() => this.setNotification("Call ended"));
                this._session = null;
                this.isModalVisible = false;
                this.isHold = false;
                if (this.setNumberCallback) {
                    this.setNumberCallback('');
                }
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                this._session.stateChange.removeListener(this.sessionListener);
                this._session = null;
                this.isModalVisible = false;
                this.isMute = false;
                this.isHold = false;
                break;

            default:
                break;
        }
    }

    onCallAccept() {
        if (!this._session) return;

        const constraints = {
            audio: true,
            video: false,
        };

        const options = {
            sessionDescriptionHandlerOptions: {
                constraints,
                iceGatheringTimeout: 100,
            },
        };

        this._session
            .accept(options)
            .then(() => {
                this.setNotification("Call accepted");
                this.isModalVisible = false;
            })
            .catch((error) => this.setError(error.message));
    }

    attachMedia() {
        if (!this._session || !this.mediaElement?.current) return;

        try {

            const remoteStream = new MediaStream();

            const pc = this._session.sessionDescriptionHandler.peerConnection;

            pc.getReceivers().forEach((receiver) => {
                if (receiver.track && receiver.track.kind === 'audio') {
                    console.log(`Adding remote track to stream: ${receiver.track.id} (${receiver.track.kind})`);
                    remoteStream.addTrack(receiver.track);
                }
            });

            pc.ontrack = (event) => {
                console.log(`New track added: ${event.track.id} (${event.track.kind})`);
                if (event.track.kind === 'audio') {
                    remoteStream.addTrack(event.track);
                }
            };

            this.mediaElement.current.srcObject = remoteStream;

            this.mediaElement.current.muted = false;
            this.mediaElement.current.volume = 1.0;

            this.mediaElement.current.play()
                .then(() => console.log("Media playback started successfully"))
                .catch(e => {
                    console.error("Error playing media:", e);

                    setTimeout(() => {
                        console.log("Retrying media playback...");
                        this.mediaElement.current.play()
                            .catch(retryError => console.error("Retry failed:", retryError));
                    }, 1000);
                });
        } catch (error) {
            console.error("Error attaching media:", error);
        }
    }

    cleanupMedia() {
        if (!this.mediaElement?.current) return;

        try {
            if (this.mediaElement.current.srcObject !== null) {
                this.mediaElement.current.srcObject = null;
            }

            if (typeof this.mediaElement.current.pause === 'function') {
                this.mediaElement.current.pause();
            }
        } catch (error) {
            console.warn('Error cleaning up media:', error);
        }
    }

    setMediaElement(element) {
        this.mediaElement = element;
    }

    setAudioElement(element) {
        this.audioElement = element;
    }
}


class SipService {
    constructor() {
        this.sipModule = null;
        this.callbacks = {
            onCallReceived: null,
            onCallConnected: null,
            onCallEnded: null,
            onRegistered: null,
            onUnregistered: null,
            onRegistrationFailed: null
        };
        this.audioElement = null;
        this.mediaElement = null;
        this.initialized = false;
        this._reconnectTimer = null;
        this._reconnectAttempts = 0;
        this._maxReconnectAttempts = 5;
        this._wasRegistered = false;
    }

    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }
    async initialize() {
        try {
            if (!this.audioElement) {
                this.audioElement = document.createElement('audio');
                this.audioElement.src = '/telephone_ring.mp3';
                this.audioElement.loop = true;
                this.audioElement.preload = 'auto';
                document.body.appendChild(this.audioElement);

                this.audioElement.play()
                    .then(() => {
                        console.log("Ringtone audio initialized successfully");
                        this.audioElement.pause();
                        this.audioElement.currentTime = 0;
                    })
                    .catch(e => {
                        console.warn("Ringtone audio initialization failed, may need user interaction:", e);
                    });
            }

            if (!this.mediaElement) {
                this.mediaElement = document.createElement('audio');
                this.mediaElement.autoplay = true;
                this.mediaElement.controls = false;
                this.mediaElement.style.display = 'none';
                this.mediaElement.preload = 'auto';

                if (typeof this.mediaElement.setSinkId === 'function') {
                    this.mediaElement.setSinkId('default')
                        .catch(e => console.warn("Could not set audio output device:", e));
                }

                document.body.appendChild(this.mediaElement);

                this.mediaElement.play()
                    .then(() => {
                        console.log("Call audio initialized successfully");
                        this.mediaElement.pause();
                    })
                    .catch(e => {
                        console.warn("Call audio initialization failed, may need user interaction:", e);
                    });
            }
            let sipConfig = null;

            try {
                const storedConfig = sessionStorage.getItem('sipConfig');
                if (storedConfig) {
                    sipConfig = JSON.parse(storedConfig);
                    console.log('Using SIP configuration from session storage');
                } else {
                    console.warn('No SIP configuration found in session storage');

                    const currentUser = sessionStorage.getItem('currentUser');
                    if (!currentUser) {
                        throw new Error('User not logged in. Cannot initialize SIP service.');
                    }

                    const userData = JSON.parse(currentUser);
                    if (!userData.auth_username || !userData.auth_password) {
                        throw new Error('SIP credentials missing from user data');
                    }

                    sipConfig = {
                        sipDomain: import.meta.env.VITE_SIP_DOMAIN || 'solutionsv2.tclcontactplus.com',
                        name: userData.name || 'Agent',
                        authUser: userData.auth_username,
                        authPass: userData.auth_password,
                        wssPort: import.meta.env.VITE_SIP_PORT || '8089'
                    };

                    sessionStorage.setItem('sipConfig', JSON.stringify(sipConfig));
                }
            } catch (error) {
                console.error('Error with SIP configuration:', error);
                throw new Error('Failed to initialize SIP service: ' + error.message);
            }

            if (!sipConfig || !sipConfig.authUser || !sipConfig.authPass) {
                const errorMsg = 'Invalid SIP configuration: missing required credentials';
                console.error(errorMsg);
                throw new Error(errorMsg);
            }

            this.sipModule = new SIPModule({
                ...sipConfig,
                queues: [],
                setNumberCallback: (number) => {
                    console.log('Incoming call from:', number);
                }
            });
            const mediaRef = { current: this.mediaElement };
            const audioRef = { current: this.audioElement };
            this.sipModule.setMediaElement(mediaRef);
            this.sipModule.setAudioElement(audioRef);

            this.sipModule.setNotification = (message) => {
                console.log('SIP Notification:', message);
            };

            this.sipModule.setError = (error) => {
                console.error('SIP Error:', error);
                if (error.includes('registration') && this.callbacks.onRegistrationFailed) {
                    this.callbacks.onRegistrationFailed(new Error(error));
                }
            };
            const originalOnInvite = this.sipModule.onInvite.bind(this.sipModule);
            this.sipModule.onInvite = (invitation) => {
                originalOnInvite(invitation);

                if (this.callbacks.onCallReceived) {
                    this.callbacks.onCallReceived(invitation);
                }
            };

            this.sipModule.start();
            this.initialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing SIP service:', error);
            return false;
        }
    }

    async register() {
        if (!this.sipModule) {
            console.error('SIP module not initialized');
            return false;
        }

        try {
            console.log('Setting up SIP event listeners...');

            const originalSessionListener = this.sipModule.sessionListener;
            this.sipModule.sessionListener = (state) => {
                originalSessionListener(state);

                if (state === SessionState.Established && this.callbacks.onCallConnected) {
                    this.callbacks.onCallConnected();
                } else if ((state === SessionState.Terminating || state === SessionState.Terminated) &&
                    this.callbacks.onCallEnded) {
                    let reason = 'unknown';

                    if (this.sipModule._session) {
                        const cause = this.sipModule._session.terminationReason?.cause;
                        console.log('Session termination cause:', cause);

                        if (cause === 16) {
                            if (this.sipModule._session.localHold) {
                                reason = 'local';
                            } else {
                                reason = 'remote';
                            }
                        } else if (cause === 17) {
                            reason = 'remote';
                        } else if (cause === 18) {
                            reason = 'remote';
                        } else if (cause === 19) {
                            reason = 'remote';
                        } else if (cause === 20) {
                            reason = 'remote';
                        } else if (cause === 27) {
                            reason = 'remote';
                        } else if (cause === 31) {
                            reason = 'remote';
                        } else if (this.sipModule._session.endTime) {
                            reason = 'remote';
                        } else {
                            reason = 'terminated';
                        }
                    }

                    console.log('Call ended with reason:', reason);
                    this.callbacks.onCallEnded(reason);
                }
            };

            const originalRegisterListener = this.sipModule.registerListener;
            this.sipModule.registerListener = (state) => {
                originalRegisterListener(state);

                if (state === RegistererState.Registered && this.callbacks.onRegistered) {
                    this.callbacks.onRegistered();
                } else if ((state === RegistererState.Unregistered || state === RegistererState.Terminated) &&
                    this.callbacks.onUnregistered) {
                    this.callbacks.onUnregistered();
                }
            };

            const originalOnDisconnect = this.sipModule.onDisconnect.bind(this.sipModule);
            this.sipModule.onDisconnect = (error) => {
                originalOnDisconnect(error);

                if (this._wasRegistered) {
                    console.log('SIP disconnected, attempting to reconnect in 3 seconds...');
                    setTimeout(() => {
                        if (this.sipModule && !this.sipModule._connected) {
                            console.log('Attempting SIP reconnection...');
                            try {
                                this.sipModule.userAgent.start()
                                    .then(() => {
                                        console.log('SIP transport reconnected, re-registering...');
                                        this.sipModule.registerSIP();
                                    })
                                    .catch(err => {
                                        console.error('Failed to reconnect SIP transport:', err);
                                        if (this.callbacks.onRegistrationFailed) {
                                            this.callbacks.onRegistrationFailed(err);
                                        }
                                    });
                            } catch (reconnectError) {
                                console.error('Error during SIP reconnection attempt:', reconnectError);
                            }
                        }
                    }, 3000);
                } else {
                    console.log('SIP disconnected, not attempting to reconnect because user was not registered');
                }
            };

            console.log('Registering with SIP server...');
            this.sipModule.registerSIP();

            await new Promise(resolve => setTimeout(resolve, 1000));

            if (this.sipModule._registered) {
                console.log('SIP registration confirmed');
                this._wasRegistered = true;
                return true;
            } else {
                console.warn('SIP registration not confirmed after attempt');
                this._wasRegistered = false;
                return false;
            }
        } catch (error) {
            console.error('Error registering with SIP server:', error);
            if (this.callbacks.onRegistrationFailed) {
                this.callbacks.onRegistrationFailed(error);
            }
            this._wasRegistered = false;
            return false;
        }
    }

    async unregister() {
        if (!this.sipModule) {
            console.error('SIP module not initialized');
            return false;
        }

        try {
            console.log('Unregistering from SIP server...');

            this._wasRegistered = false;

            this.sipModule.unregisterSIP();

            await new Promise(resolve => setTimeout(resolve, 1000));
            if (!this.sipModule._registered) {
                console.log('SIP unregistration confirmed');
                return true;
            } else {
                console.warn('SIP unregistration not confirmed after attempt');
                return false;
            }
        } catch (error) {
            console.error('Error unregistering from SIP server:', error);
            return false;
        }
    }

    async makeCall(number) {
        if (!this.sipModule) return false;

        try {
            const formattedNumber = number.replace(/\s+/g, '').replace(/[^\d+]/g, '');

            if (!formattedNumber) {
                console.error('Invalid phone number');
                return false;
            }

            console.log(`Making call to formatted number: ${formattedNumber}`);

            if (!this.sipModule._connected || !this.sipModule._registered) {
                console.warn('SIP not connected or registered, attempting to register first...');

                try {
                    await new Promise((resolve, reject) => {
                        this.sipModule.userAgent.start()
                            .then(() => {
                                this.sipModule.registerSIP();
                                setTimeout(resolve, 1000);
                            })
                            .catch(reject);
                    });
                } catch (regError) {
                    console.error('Failed to register before making call:', regError);
                    return false;
                }
            }
            this.sipModule.onMakeCall(formattedNumber);
            return true;
        } catch (error) {
            console.error('Error making call:', error);
            return false;
        }
    }

    async answerCall() {
        if (!this.sipModule) return false;

        try {
            this.sipModule.onCallAccept();
            return true;
        } catch (error) {
            console.error('Error answering call:', error);
            return false;
        }
    }

    async rejectCall() {
        if (!this.sipModule || !this.sipModule._session) return false;

        try {
            this.sipModule._session.reject();
            return true;
        } catch (error) {
            console.error('Error rejecting call:', error);
            return false;
        }
    }

    async hangupCall() {
        if (!this.sipModule) {
            console.error('SIP module not initialized');
            return false;
        }

        try {
            console.log('Hanging up call via SIP module...');

            if (!this.sipModule._session) {
                console.warn('No active call session to hang up');
                return true;
            }

            const sessionState = this.sipModule._session.state;
            console.log(`Current session state before hangup: ${sessionState}`);

            if (sessionState === SessionState.Established) {
                await this.sipModule._session.bye();
                console.log('Call terminated with BYE');
            } else if (sessionState === SessionState.Establishing) {
                if (this.sipModule._session instanceof Inviter) {
                    await this.sipModule._session.cancel();
                    console.log('Outgoing call cancelled');
                } else {
                    await this.sipModule._session.reject();
                    console.log('Incoming call rejected');
                }
            } else {
                this.sipModule.onEndCall();
                console.log('Call ended via standard method');
            }

            await new Promise(resolve => setTimeout(resolve, 1000));
            if (this.sipModule._session) {
                console.warn('Session still exists after hangup, forcing cleanup');

                this.sipModule._session.stateChange.removeAllListeners();
                this.sipModule.cleanupMedia();
                this.sipModule._session = null;
                this.sipModule.isConnected = false;
                this.sipModule.callAnswered = false;
                this.sipModule.isModalVisible = false;
                this.sipModule.isHold = false;
                this.sipModule.isMute = false;

                return true;
            } else {
                console.log('Call successfully hung up');
                return true;
            }
        } catch (error) {
            console.error('Error hanging up call:', error);

            try {
                if (this.sipModule._session) {
                    this.sipModule._session.stateChange.removeAllListeners();
                    this.sipModule.cleanupMedia();
                    this.sipModule._session = null;
                    this.sipModule.isConnected = false;
                    this.sipModule.callAnswered = false;
                    this.sipModule.isModalVisible = false;
                    this.sipModule.isHold = false;
                    this.sipModule.isMute = false;
                }
                return true;
            } catch (cleanupError) {
                console.error('Error during forced cleanup:', cleanupError);
                return false;
            }
        }
    }

    async holdCall() {
        if (!this.sipModule) return false;

        try {
            await this.sipModule.onHold();
            return true;
        } catch (error) {
            console.error('Error holding call:', error);
            return false;
        }
    }

    async resumeCall() {
        if (!this.sipModule) return false;

        try {
            await this.sipModule.onUnhold();
            return true;
        } catch (error) {
            console.error('Error resuming call:', error);
            return false;
        }
    }

    async mute() {
        if (!this.sipModule) return false;

        try {
            this.sipModule.onMute();
            return true;
        } catch (error) {
            console.error('Error muting call:', error);
            return false;
        }
    }

    async unmute() {
        if (!this.sipModule) return false;

        try {
            this.sipModule.onUnmute();
            return true;
        } catch (error) {
            console.error('Error unmuting call:', error);
            return false;
        }
    }

    async sendDTMF(digit) {
        if (!this.sipModule) return false;

        try {
            this.sipModule.onDTMFInput(digit);
            return true;
        } catch (error) {
            console.error('Error sending DTMF tone:', error);
            return false;
        }
    }

    async transferCall(number) {
        if (!this.sipModule) return false;

        try {
            this.sipModule.onBlindTransfer(number);
            return true;
        } catch (error) {
            console.error('Error transferring call:', error);
            return false;
        }
    }

    terminate() {
        if (this.sipModule) {
            try {
                this.sipModule.stop();
            } catch (error) {
                console.error('Error stopping SIP module:', error);
            }
        }

        if (this.audioElement && this.audioElement.parentNode) {
            this.audioElement.parentNode.removeChild(this.audioElement);
        }

        if (this.mediaElement && this.mediaElement.parentNode) {
            this.mediaElement.parentNode.removeChild(this.mediaElement);
        }

        this.sipModule = null;
        this.initialized = false;
    }
}


const sipService = new SipService();
export default sipService;