import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { X } from 'lucide-react';
import { useTicketStore } from '../store/ticketStore';

const ticketSchema = z.object({
    title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
    description: z.string().min(1, 'Description is required').max(2000, 'Description must be less than 2000 characters'),
    priority: z.enum(['low', 'medium', 'high', 'critical']),
    category: z.string().min(1, 'Category is required'),
    assigneeId: z.string().min(1, 'Assignee is required'),
    dueDate: z.string().optional(),
    tags: z.string().optional()
});

export default function CreateTicketModal({ isOpen, onClose }) {
    const { users, categories, createTicket } = useTicketStore();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        reset
    } = useForm({
        resolver: zodResolver(ticketSchema),
        defaultValues: {
            priority: 'medium',
            category: categories[0],
            assigneeId: users[0]?.id || '',
            tags: ''
        }
    });

    const onSubmit = async (data) => {
        setIsSubmitting(true);
        try {
            const ticketData = {
                ...data,
                tags: data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
                reporterId: '1', // Current user (demo)
                dueDate: data.dueDate ? new Date(data.dueDate).toISOString() : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // Default 7 days
            };
            
            createTicket(ticketData);
            reset();
            onClose();
        } catch (error) {
            console.error('Error creating ticket:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle>Create New Ticket</CardTitle>
                            <CardDescription>
                                Fill in the details to create a new support ticket
                            </CardDescription>
                        </div>
                        <Button variant="ghost" size="sm" onClick={onClose}>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="md:col-span-2">
                                <Label htmlFor="title">Title *</Label>
                                <Input
                                    id="title"
                                    {...register('title')}
                                    placeholder="Brief description of the issue"
                                    className="mt-1"
                                />
                                {errors.title && (
                                    <p className="text-sm text-red-500 mt-1">{errors.title.message}</p>
                                )}
                            </div>

                            <div className="md:col-span-2">
                                <Label htmlFor="description">Description *</Label>
                                <textarea
                                    id="description"
                                    {...register('description')}
                                    placeholder="Detailed description of the issue, steps to reproduce, expected behavior, etc."
                                    rows={4}
                                    className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="priority">Priority *</Label>
                                <Select 
                                    value={watch('priority')} 
                                    onValueChange={(value) => setValue('priority', value)}
                                >
                                    <SelectTrigger className="mt-1">
                                        <SelectValue placeholder="Select priority" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="low">Low</SelectItem>
                                        <SelectItem value="medium">Medium</SelectItem>
                                        <SelectItem value="high">High</SelectItem>
                                        <SelectItem value="critical">Critical</SelectItem>
                                    </SelectContent>
                                </Select>
                                {errors.priority && (
                                    <p className="text-sm text-red-500 mt-1">{errors.priority.message}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="category">Category *</Label>
                                <Select 
                                    value={watch('category')} 
                                    onValueChange={(value) => setValue('category', value)}
                                >
                                    <SelectTrigger className="mt-1">
                                        <SelectValue placeholder="Select category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categories.map(category => (
                                            <SelectItem key={category} value={category}>{category}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.category && (
                                    <p className="text-sm text-red-500 mt-1">{errors.category.message}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="assigneeId">Assign to *</Label>
                                <Select 
                                    value={watch('assigneeId')} 
                                    onValueChange={(value) => setValue('assigneeId', value)}
                                >
                                    <SelectTrigger className="mt-1">
                                        <SelectValue placeholder="Select assignee" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {users.map(user => (
                                            <SelectItem key={user.id} value={user.id}>
                                                {user.name} ({user.role})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.assigneeId && (
                                    <p className="text-sm text-red-500 mt-1">{errors.assigneeId.message}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="dueDate">Due Date</Label>
                                <Input
                                    id="dueDate"
                                    type="datetime-local"
                                    {...register('dueDate')}
                                    className="mt-1"
                                    min={new Date().toISOString().slice(0, 16)}
                                />
                                {errors.dueDate && (
                                    <p className="text-sm text-red-500 mt-1">{errors.dueDate.message}</p>
                                )}
                            </div>

                            <div className="md:col-span-2">
                                <Label htmlFor="tags">Tags</Label>
                                <Input
                                    id="tags"
                                    {...register('tags')}
                                    placeholder="Enter tags separated by commas (e.g., bug, mobile, urgent)"
                                    className="mt-1"
                                />
                                <p className="text-sm text-gray-500 mt-1">
                                    Separate multiple tags with commas
                                </p>
                                {errors.tags && (
                                    <p className="text-sm text-red-500 mt-1">{errors.tags.message}</p>
                                )}
                            </div>
                        </div>

                        <div className="flex justify-end gap-3 pt-4 border-t">
                            <Button type="button" variant="outline" onClick={onClose}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Creating...' : 'Create Ticket'}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    );
}
