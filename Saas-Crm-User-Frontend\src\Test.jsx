
import {
    Invitation,
    Inviter,
    Registerer,
    RegistererState,
    SessionState,
    TransportState,
    UserAgent,
    Web,
} from "sip.js";

export default class SIPModule {
    constructor({
        sipDomain,
        name,
        authUser,
        authPass,
        wssPort,
        queues,
        setNumberCallback
    }) {
        this.sipDomain = sipDomain;
        this.name = name;
        this.authUser = authUser;
        this.authPass = authPass;
        this.wssPort = wssPort;
        this.queues = queues;
        this.setNumberCallback = setNumberCallback;

        this.uri = UserAgent.makeURI(`sip:${authUser}@${sipDomain}`);

        this._transferredSession = null;
        this._session = null;
        this._connected = false;
        this._registered = false;

        this.userAgent = null;
        this.registerer = null;
        this.isModalVisible = false;
        this.dialedNumber = "";
        this.incoming = false;
        this.outgoing = false;
        this.isConnected = false;
        this.isHold = false;
        this.isMute = false;
        this.sessionState = null;
        this.isTransferHold = false;
        this.isTransferMute = false;
        this.isTransferConnected = false;
        this.isBridged = false;
        this.incomingCallAnswered = false;
        this.callAnswered = false;

        this.mediaElement = null;
        this.audioElement = null;
        this.onInvite = this.onInvite.bind(this);
        this.onConnect = this.onConnect.bind(this);
        this.onDisconnect = this.onDisconnect.bind(this);
        this.onRegister = this.onRegister.bind(this);
        this.sessionListener = this.sessionListener.bind(this);
        this.incomingSessionListener = this.incomingSessionListener.bind(this);
        this.registerListener = this.registerListener.bind(this);
    }

    initialize() {
        this.userAgent = new UserAgent({
            authorizationUsername: this.authUser,
            authorizationPassword: this.authPass,
            transportOptions: {
                server: `wss://${this.sipDomain}:${this.wssPort}/ws`,
            },
            uri: this.uri,
            logLevel: "error",
            delegate: {
                onConnect: this.onConnect,
                onDisconnect: this.onDisconnect,
                onRegister: this.onRegister,
                onInvite: this.onInvite,
            },
        });

        this.registerer = new Registerer(this.userAgent);
    }

    start() {
        this.initialize();
    }

    stop() {
        if (this.registerer) {
            this.registerer.stateChange.removeListener(this.registerListener);
        }

        if (this._session) {
            this._session.stateChange.removeListener(this.sessionListener);
        }

        if (this.userAgent) {
            this.userAgent.stop();
        }
    }

    setError(error) {
        console.error("SIP Error:", error);
    }

    setNotification(message) {
        console.log("SIP Notification:", message);
    }

    playRinger() {
        if (this.audioElement) {
            this.audioElement.play().catch(e => console.error("Error playing ringtone:", e));
        }
    }

    stopRing() {
        if (this.audioElement) {
            this.audioElement.pause();
            this.audioElement.currentTime = 0;
        }
    }

    onInvite(invitation) {
        if (!this._session) {
            this.playRinger();

            this._session = invitation;
            this.isModalVisible = true;
            this.registerIncomingEvents();

            const dialedNumber = invitation.remoteIdentity.uri.user;
            this.dialedNumber = dialedNumber;
            this.incoming = true;

            if (this.setNumberCallback) {
                this.setNumberCallback(dialedNumber);
            }
        } else {
            invitation
                .reject()
                .then(() => {
                    this.setNotification(`Incoming call from ${invitation.remoteIdentity.uri.user} rejected due to client busy.`);
                })
                .catch((e) => this.setError(e));
        }
    }

    onTransferHold() {
        if (!this._transferredSession) return;

        const options = {
            sessionDescriptionHandlerModifiers: [Web.holdModifier],
        };

        this._transferredSession
            .invite(options)
            .then(() => this.isTransferHold = true)
            .catch((err) => this.setError(err.message));
    }

    onTransferUnhold() {
        if (!this._transferredSession) return;

        const options = {
            sessionDescriptionHandlerModifiers: [],
        };

        this._transferredSession
            .invite(options)
            .then(() => this.isTransferHold = false)
            .catch((err) => this.setError(err.message));
    }

    onTransferMute() {
        if (!this._transferredSession) return;

        const pc = this._transferredSession.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = false;
                this.isTransferMute = true;
            }
        });
    }

    onTransferUnmute() {
        if (!this._transferredSession) return;

        const pc = this._transferredSession.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = true;
                this.isTransferMute = false;
            }
        });
    }

    onHold() {
        if (!this._session) return;

        const options = {
            sessionDescriptionHandlerModifiers: [Web.holdModifier],
        };

        return this._session
            .invite(options)
            .then(() => this.isHold = true)
            .catch((err) => this.setError(err.message));
    }

    onUnhold() {
        if (!this._session) return;

        const options = {
            sessionDescriptionHandlerModifiers: [],
        };

        return this._session
            .invite(options)
            .then(() => this.isHold = false)
            .catch((err) => this.setError(err.message));
    }

    onMute() {
        if (!this._session) return;

        const pc = this._session.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = false;
                this.isMute = true;
            }
        });
    }

    onUnmute() {
        if (!this._session) return;

        const pc = this._session.sessionDescriptionHandler.peerConnection;
        const senders = pc.getSenders();

        senders.forEach((sender) => {
            if (sender.track) {
                sender.track.enabled = true;
                this.isMute = false;
            }
        });
    }

    onBlindTransfer(target) {
        if (!this._session) return;

        const transferTarget = UserAgent.makeURI(`sip:${target}@${this.sipDomain}`);
        if (!transferTarget) {
            throw new Error("Failed to create transfer target URI.");
        }

        this._session
            .refer(transferTarget)
            .then(() => {
                this.setNotification("Call has been transferred");
                this.onEndCall();
            })
            .catch((error) => this.setError(error.message));
    }

    onTransferHangup() {
        if (!this._transferredSession) return;

        switch (this._transferredSession.state) {
            case SessionState.Initial:
            case SessionState.Establishing:
                if (this._transferredSession instanceof Inviter) {
                    this._transferredSession
                        .cancel()
                        .then((res) => this.setNotification(res));
                } else {
                    this._transferredSession.reject();
                }
                this._transferredSession.stateChange.removeListener(this.sessionListener);
                this._transferredSession = null;
                this.isTransferMute = false;
                this.isTransferHold = false;
                this.isTransferConnected = false;
                this.isBridged = false;
                break;

            case SessionState.Established:
                this._transferredSession.bye();
                this._transferredSession.stateChange.removeListener(this.sessionListener);
                this._transferredSession = null;
                this.isTransferMute = false;
                this.isTransferHold = false;
                this.isTransferConnected = false;
                this.isBridged = false;
                this.attachMedia();
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                break;

            default:
                break;
        }
    }

    onAttendedTransfer(number) {
        if (!this._session) return;

        const target = UserAgent.makeURI(`sip:${number}@${this.sipDomain}`);
        if (!target) {
            this.setError("Failed to create transfer target URI.");
            return;
        }

        const transferSession = new Inviter(this.userAgent, target, {
            earlyMedia: true,
        });

        const constraints = {
            audio: true,
            video: false,
        };

        const options = {
            earlyMedia: true,
            sessionDescriptionHandlerOptions: {
                constraints,
            },
        };

        transferSession.stateChange.addListener((state) => {
            switch (state) {
                case SessionState.Initial:
                    break;

                case SessionState.Establishing:
                    this.isTransferConnected = true;
                    const remoteStream = new MediaStream();
                    transferSession.sessionDescriptionHandler.peerConnection
                        .getReceivers()
                        .forEach((receiver) => {
                            if (receiver.track) {
                                remoteStream.addTrack(receiver.track);
                            }
                        });

                    if (this.mediaElement?.current) {
                        this.mediaElement.current.srcObject = remoteStream;
                        this.mediaElement.current.play().catch(e => console.error("Error playing media:", e));
                    }
                    break;

                case SessionState.Established:
                    break;

                case SessionState.Terminating:
                case SessionState.Terminated:
                    this.isTransferConnected = false;
                    this._transferredSession = null;
                    break;

                default:
                    throw new Error("Unknown session state.");
            }
        });

        transferSession.invite(options).catch((e) => this.setError(e));
        this._transferredSession = transferSession;
    }

    onAcceptTransfer() {
        if (!this._session || !this._transferredSession) return;

        const receivedTracks = [];
        const sessionA = this._session;
        const sessionB = this._transferredSession;

        sessionA.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                receivedTracks.push(receiver.track);
            });

        sessionB.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                receivedTracks.push(receiver.track);
            });

        const context = new AudioContext();
        const mediaStream = new MediaStream();

        const sessions = [sessionA, sessionB];
        sessions.forEach((session) => {
            const mixedOutput = context.createMediaStreamDestination();
            session.sessionDescriptionHandler.peerConnection
                .getReceivers()
                .forEach((receiver) => {
                    receivedTracks.forEach((track) => {
                        mediaStream.addTrack(receiver.track);
                        if (receiver.track.id !== track.id) {
                            const sourceStream = context.createMediaStreamSource(
                                new MediaStream([track])
                            );
                            sourceStream.connect(mixedOutput);
                        }
                    });
                });

            session.sessionDescriptionHandler.peerConnection
                .getSenders()
                .forEach((sender) => {
                    const sourceStream = context.createMediaStreamSource(
                        new MediaStream([sender.track])
                    );
                    sourceStream.connect(mixedOutput);
                });

            session.sessionDescriptionHandler.peerConnection
                .getSenders()[0]
                .replaceTrack(mixedOutput.stream.getTracks()[0])
                .then(() => {
                    this.isBridged = true;
                    if (this.isHold) this.onUnhold();
                })
                .catch((e) => this.setError(e));
        });

        if (this.mediaElement?.current) {
            this.mediaElement.current.srcObject = mediaStream;
            this.mediaElement.current.play().catch(e => console.error("Error playing media:", e));
        }
    }

    onConnect() {
        this._connected = true;
        this.setNotification("Connected to SIP server");
    }

    onDisconnect(error) {
        this._connected = false;
        this.setError(`Disconnected from SIP server: ${error}`);
    }

    onRegister() {
        this.setNotification("Registration successful");
    }

    sessionListener(state) {
        this.sessionState = state;
        this.setNotification(`Session state changed to ${state}`);

        switch (state) {
            case SessionState.Initial:
                break;

            case SessionState.Establishing:
                this.attachMedia();
                break;

            case SessionState.Established:
                this.stopRing();
                this.isConnected = true;
                this.callAnswered = true;
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                this.stopRing();
                this.cleanupMedia();
                this.incoming = false;
                this.isConnected = false;
                this.callAnswered = false;
                this._session = null;
                this.isModalVisible = false;
                this.isHold = false;
                break;

            default:
                throw new Error("Unknown session state.");
        }
    }

    incomingSessionListener(state) {
        this.sessionState = state;
        this.setNotification(`Incoming session state changed to ${state}`);

        switch (state) {
            case SessionState.Initial:
                break;

            case SessionState.Establishing:
                break;

            case SessionState.Established:
                this.attachMedia();
                this.stopRing();
                this.isConnected = true;
                this.callAnswered = true;
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                this.stopRing();
                this.cleanupMedia();
                this.incoming = false;
                this.isConnected = false;
                this.callAnswered = false;
                this._session = null;
                this.isModalVisible = false;
                this.isHold = false;
                break;

            default:
                throw new Error("Unknown session state.");
        }
    }

    registerListener(state) {
        switch (state) {
            case RegistererState.Registered:
                this._registered = true;
                this.setNotification("Successfully registered");
                break;

            case RegistererState.Unregistered:
            case RegistererState.Terminated:
                this._registered = false;
                this.setNotification("Unregistered");
                break;

            case RegistererState.Initial:
            default:
                break;
        }
    }

    registerEvents() {
        if (this._session) {
            this._session.stateChange.addListener(this.sessionListener);
        }
    }

    registerIncomingEvents() {
        if (this._session) {
            this._session.stateChange.addListener(this.incomingSessionListener);
        }
    }

    getSession() {
        return this._session;
    }

    getSessionState() {
        return this._session?.state;
    }

    isConnected() {
        return this._connected;
    }

    isRegistered() {
        return this._registered;
    }

    registerSIP() {
        this.userAgent
            .start()
            .then(() => {
                this.userAgent.transport.stateChange.addListener((state) => {
                    switch (state) {
                        case TransportState.Disconnected:
                            this.setNotification("Transport disconnected");
                            break;
                        case TransportState.Connected:
                            this.setNotification("Transport connected");
                            break;
                        default:
                            break;
                    }
                });

                this.registerer
                    .register()
                    .then(() => {
                        this.registerer.stateChange.addListener(this.registerListener);
                    })
                    .catch((error) => this.setError(error.message));
            })
            .catch((error) => this.setError(error.message));
    }

    unregisterSIP() {
        this.registerer
            .unregister()
            .then(() => this.setNotification("Successfully unregistered"))
            .catch((error) => this.setError(error.message));
    }

    onMakeCall(number) {
        const target = UserAgent.makeURI(`sip:${number}@${this.sipDomain}`);
        if (!target) {
            this.setError("Failed to create target URI.");
            return;
        }

        const inviter = new Inviter(this.userAgent, target, {
            earlyMedia: true,
        });

        const constraints = {
            audio: true,
            video: false,
        };

        const options = {
            earlyMedia: true,
            sessionDescriptionHandlerOptions: {
                constraints,
                iceGatheringTimeout: 100,
            },
        };

        inviter
            .invite(options)
            .then(() => {
                this.setNotification(`Calling ${number}`);
                this._session = inviter;
                this.registerEvents();
            })
            .catch((error) => this.setError(error.message));
    }

    onDTMFInput(number) {
        if (!this._session) return;

        const options = {
            requestOptions: {
                body: {
                    contentDisposition: "render",
                    contentType: "application/dtmf-relay",
                    content: `Signal=${number}\r\nDuration=1000`,
                },
            },
        };

        this._session.info(options).catch(e => this.setError(e));
    }

    onEndCall() {
        this.stopRing();
        if (!this._session) return;

        switch (this._session.state) {
            case SessionState.Initial:
            case SessionState.Establishing:
                if (this._session instanceof Inviter) {
                    this._session.cancel().then(() => this.setNotification("Call cancelled"));
                } else {
                    this._session.reject();
                }
                this._session = null;
                this.isModalVisible = false;
                break;

            case SessionState.Established:
                this._session.bye().then(() => this.setNotification("Call ended"));
                this._session = null;
                this.isModalVisible = false;
                this.isHold = false;
                if (this.setNumberCallback) {
                    this.setNumberCallback('');
                }
                break;

            case SessionState.Terminating:
            case SessionState.Terminated:
                this._session.stateChange.removeListener(this.sessionListener);
                this._session = null;
                this.isModalVisible = false;
                this.isMute = false;
                this.isHold = false;
                break;

            default:
                break;
        }
    }



    onCallAccept() {
        if (!this._session) return;

        const constraints = {
            audio: true,
            video: false
        };

        console.log('call accwept')

        const options = {
            sessionDescriptionHandlerOptions: {
                constraints,
                iceGatheringTimeout: 10000,
            },
            sessionDescriptionHandlerModifiers: [
                (description) => {
                    description.sdp = description.sdp.replace(
                        /a=sendrecv/g,
                        'a=sendrecv'
                    );
                    return description;
                }
            ]
        };

        return this._session.accept(options)
            .then(() => {
                this.attachMedia();
                this.setNotification("Call accepted");
            })
            .catch(error => {
                this.setError(error.message);
                throw error;
            });
    }


    attachMedia() {
        if (!this._session || !this.mediaElement?.current) return;

        const session = this._session;
        const pc = session.sessionDescriptionHandler.peerConnection;

        // Create a new media stream for remote audio
        const remoteStream = new MediaStream();

        // Add all received tracks to the stream
        pc.getReceivers().forEach(receiver => {
            if (receiver.track && receiver.track.kind === 'audio') {
                remoteStream.addTrack(receiver.track);
            }
        });

        // Handle new tracks as they're added
        pc.ontrack = (event) => {
            if (event.track.kind === 'audio') {
                remoteStream.addTrack(event.track);
            }
        };

        // Set the stream to the audio element
        this.mediaElement.current.srcObject = remoteStream;

        // Play the audio
        this.mediaElement.current.play().catch(error => {
            console.error('Error playing audio:', error);
        });
    }
    cleanupMedia() {
        if (!this.mediaElement?.current) return;

        this.mediaElement.current.srcObject = null;
        this.mediaElement.current.pause();
    }

    setMediaElement(element) {
        this.mediaElement = element;
    }

    setAudioElement(element) {
        this.audioElement = element;
    }
}