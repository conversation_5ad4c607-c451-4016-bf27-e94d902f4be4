import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { CheckCircle, Users, Clock, BarChart3, Shield, Zap } from 'lucide-react';

export default function IndexPage() {
    const navigate = useNavigate();

    const features = [
        {
            icon: <Users className="h-8 w-8 text-blue-600" />,
            title: "Team Collaboration",
            description: "Seamlessly collaborate with your team members on tickets and projects."
        },
        {
            icon: <Clock className="h-8 w-8 text-green-600" />,
            title: "Real-time Updates",
            description: "Get instant notifications and updates on ticket status changes."
        },
        {
            icon: <BarChart3 className="h-8 w-8 text-purple-600" />,
            title: "Advanced Analytics",
            description: "Track performance metrics and generate detailed reports."
        },
        {
            icon: <Shield className="h-8 w-8 text-red-600" />,
            title: "Enterprise Security",
            description: "Bank-level security with role-based access control."
        },
        {
            icon: <Zap className="h-8 w-8 text-yellow-600" />,
            title: "Lightning Fast",
            description: "Optimized performance for handling thousands of tickets."
        }
    ];

    const pricingPlans = [
        {
            name: "Starter",
            price: "$9",
            period: "/month",
            description: "Perfect for small teams getting started",
            features: [
                "Up to 5 team members",
                "100 tickets per month",
                "Basic reporting",
                "Email support",
                "Mobile app access"
            ],
            popular: false,
            buttonText: "Start Free Trial"
        },
        {
            name: "Professional",
            price: "$29",
            period: "/month",
            description: "Ideal for growing businesses",
            features: [
                "Up to 25 team members",
                "Unlimited tickets",
                "Advanced analytics",
                "Priority support",
                "Custom integrations",
                "API access",
                "Advanced workflows"
            ],
            popular: true,
            buttonText: "Start Free Trial"
        },
        {
            name: "Enterprise",
            price: "$99",
            period: "/month",
            description: "For large organizations with complex needs",
            features: [
                "Unlimited team members",
                "Unlimited tickets",
                "White-label solution",
                "24/7 phone support",
                "Custom development",
                "On-premise deployment",
                "Advanced security features",
                "Dedicated account manager"
            ],
            popular: false,
            buttonText: "Contact Sales"
        }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            {/* Header */}
            <header className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <h1 className="text-2xl font-bold text-gray-900">Accord</h1>
                                <p className="text-sm text-gray-500">Ticket Management System</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="outline"
                                onClick={() => navigate('/login')}
                            >
                                Sign In
                            </Button>
                            <Button onClick={() => navigate('/login')}>
                                Get Started
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                        Streamline Your
                        <span className="text-blue-600"> Ticket Management</span>
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                        Accord helps teams manage support tickets, track issues, and deliver exceptional customer service with powerful automation and analytics.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                            size="lg"
                            className="text-lg px-8 py-3"
                            onClick={() => navigate('/login')}
                        >
                            Start Free Trial
                        </Button>
                        <Button
                            variant="outline"
                            size="lg"
                            className="text-lg px-8 py-3"
                        >
                            Watch Demo
                        </Button>
                    </div>
                    <p className="text-sm text-gray-500 mt-4">
                        No credit card required • 14-day free trial
                    </p>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Everything you need to manage tickets efficiently
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Powerful features designed to help your team stay organized and deliver exceptional support.
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        {feature.icon}
                                    </div>
                                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription className="text-base">
                                        {feature.description}
                                    </CardDescription>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Pricing Section */}
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Simple, transparent pricing
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Choose the perfect plan for your team. All plans include a 14-day free trial.
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {pricingPlans.map((plan, index) => (
                            <Card key={index} className={`relative ${plan.popular ? 'ring-2 ring-blue-500 shadow-lg' : ''}`}>
                                {plan.popular && (
                                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                                        Most Popular
                                    </Badge>
                                )}
                                <CardHeader className="text-center">
                                    <CardTitle className="text-2xl">{plan.name}</CardTitle>
                                    <div className="mt-4">
                                        <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                                        <span className="text-gray-500">{plan.period}</span>
                                    </div>
                                    <CardDescription className="mt-2">
                                        {plan.description}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ul className="space-y-3 mb-6">
                                        {plan.features.map((feature, featureIndex) => (
                                            <li key={featureIndex} className="flex items-center">
                                                <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                                <span className="text-gray-700">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                    <Button
                                        className={`w-full ${plan.popular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                                        variant={plan.popular ? 'default' : 'outline'}
                                        onClick={() => navigate('/login')}
                                    >
                                        {plan.buttonText}
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-blue-600">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-bold text-white mb-4">
                        Ready to transform your ticket management?
                    </h2>
                    <p className="text-xl text-blue-100 mb-8">
                        Join thousands of teams already using Accord to deliver exceptional support.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                            size="lg"
                            variant="secondary"
                            className="text-lg px-8 py-3"
                            onClick={() => navigate('/login')}
                        >
                            Start Your Free Trial
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            className="text-lg px-8 py-3 text-white border-white hover:bg-white hover:text-blue-600"
                        >
                            Contact Sales
                        </Button>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-gray-900 text-white py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div>
                            <h3 className="text-lg font-semibold mb-4">Accord</h3>
                            <p className="text-gray-400">
                                The most powerful ticket management system for modern teams.
                            </p>
                        </div>
                        <div>
                            <h4 className="text-sm font-semibold mb-4 uppercase tracking-wider">Product</h4>
                            <ul className="space-y-2 text-gray-400">
                                <li><a href="#" className="hover:text-white">Features</a></li>
                                <li><a href="#" className="hover:text-white">Pricing</a></li>
                                <li><a href="#" className="hover:text-white">API</a></li>
                                <li><a href="#" className="hover:text-white">Integrations</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="text-sm font-semibold mb-4 uppercase tracking-wider">Company</h4>
                            <ul className="space-y-2 text-gray-400">
                                <li><a href="#" className="hover:text-white">About</a></li>
                                <li><a href="#" className="hover:text-white">Blog</a></li>
                                <li><a href="#" className="hover:text-white">Careers</a></li>
                                <li><a href="#" className="hover:text-white">Contact</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="text-sm font-semibold mb-4 uppercase tracking-wider">Support</h4>
                            <ul className="space-y-2 text-gray-400">
                                <li><a href="#" className="hover:text-white">Help Center</a></li>
                                <li><a href="#" className="hover:text-white">Documentation</a></li>
                                <li><a href="#" className="hover:text-white">Status</a></li>
                                <li><a href="#" className="hover:text-white">Security</a></li>
                            </ul>
                        </div>
                    </div>
                    <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                        <p>&copy; 2024 Accord. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </div>
    );
}
