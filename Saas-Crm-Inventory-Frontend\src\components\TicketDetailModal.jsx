import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { X, Clock, User, Calendar, Tag, MessageSquare, Edit, Save, XCircle } from 'lucide-react';
import { useTicketStore } from '../store/ticketStore';

export default function TicketDetailModal({ ticketId, isOpen, onClose }) {
    const { getTicketById, getUserById, users, updateTicket, addComment, statuses, priorities } = useTicketStore();
    const [isEditing, setIsEditing] = useState(false);
    const [newComment, setNewComment] = useState('');
    const [isSubmittingComment, setIsSubmittingComment] = useState(false);

    const ticket = getTicketById(ticketId);
    const assignee = ticket ? getUserById(ticket.assigneeId) : null;
    const reporter = ticket ? getUserById(ticket.reporterId) : null;

    const { register, handleSubmit, setValue, watch, reset } = useForm({
        defaultValues: ticket ? {
            title: ticket.title,
            description: ticket.description,
            status: ticket.status,
            priority: ticket.priority,
            assigneeId: ticket.assigneeId,
            category: ticket.category
        } : {}
    });

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };

    const formatTimeAgo = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

        if (diffInHours < 1) return 'Less than an hour ago';
        if (diffInHours < 24) return `${diffInHours} hours ago`;
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays} days ago`;
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            'open': { color: 'bg-blue-100 text-blue-800', icon: <Clock className="h-3 w-3" /> },
            'in-progress': { color: 'bg-orange-100 text-orange-800', icon: <Clock className="h-3 w-3" /> },
            'resolved': { color: 'bg-green-100 text-green-800', icon: <Clock className="h-3 w-3" /> },
            'closed': { color: 'bg-gray-100 text-gray-800', icon: <Clock className="h-3 w-3" /> },
            'on-hold': { color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="h-3 w-3" /> }
        };

        const config = statusConfig[status] || statusConfig['open'];
        return (
            <Badge className={`${config.color} flex items-center gap-1`}>
                {config.icon}
                {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
            </Badge>
        );
    };

    const getPriorityBadge = (priority) => {
        const priorityConfig = {
            'low': 'bg-gray-100 text-gray-800',
            'medium': 'bg-yellow-100 text-yellow-800',
            'high': 'bg-orange-100 text-orange-800',
            'critical': 'bg-red-100 text-red-800'
        };

        return (
            <Badge className={priorityConfig[priority] || priorityConfig['medium']}>
                {priority.charAt(0).toUpperCase() + priority.slice(1)}
            </Badge>
        );
    };

    const onSubmitEdit = (data) => {
        updateTicket(ticketId, data);
        setIsEditing(false);
    };

    const handleAddComment = async () => {
        if (!newComment.trim()) return;

        setIsSubmittingComment(true);
        try {
            addComment(ticketId, {
                userId: '1', // Current user (demo)
                content: newComment.trim()
            });
            setNewComment('');
        } catch (error) {
            console.error('Error adding comment:', error);
        } finally {
            setIsSubmittingComment(false);
        }
    };

    if (!isOpen || !ticket) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div>
                                <div className="flex items-center gap-2 mb-2">
                                    <CardTitle className="text-xl">{ticket.id}</CardTitle>
                                    {getStatusBadge(ticket.status)}
                                    {getPriorityBadge(ticket.priority)}
                                </div>
                                <CardDescription className="text-lg font-medium text-gray-900">
                                    {isEditing ? (
                                        <Input
                                            {...register('title')}
                                            className="text-lg font-medium"
                                        />
                                    ) : (
                                        ticket.title
                                    )}
                                </CardDescription>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            {isEditing ? (
                                <>
                                    <Button size="sm" onClick={handleSubmit(onSubmitEdit)}>
                                        <Save className="h-4 w-4 mr-2" />
                                        Save
                                    </Button>
                                    <Button size="sm" variant="outline" onClick={() => {
                                        setIsEditing(false);
                                        reset();
                                    }}>
                                        <XCircle className="h-4 w-4 mr-2" />
                                        Cancel
                                    </Button>
                                </>
                            ) : (
                                <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                </Button>
                            )}
                            <Button variant="ghost" size="sm" onClick={onClose}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <Tabs defaultValue="details" className="space-y-4">
                        <TabsList>
                            <TabsTrigger value="details">Details</TabsTrigger>
                            <TabsTrigger value="comments">
                                Comments ({ticket.comments?.length || 0})
                            </TabsTrigger>
                            <TabsTrigger value="activity">Activity</TabsTrigger>
                        </TabsList>

                        <TabsContent value="details" className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="md:col-span-2 space-y-4">
                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Description</Label>
                                        {isEditing ? (
                                            <textarea
                                                {...register('description')}
                                                rows={6}
                                                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            />
                                        ) : (
                                            <div className="mt-1 p-3 bg-gray-50 rounded-md">
                                                <p className="text-gray-900 whitespace-pre-wrap">{ticket.description}</p>
                                            </div>
                                        )}
                                    </div>

                                    {ticket.tags && ticket.tags.length > 0 && (
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Tags</Label>
                                            <div className="mt-1 flex flex-wrap gap-2">
                                                {ticket.tags.map((tag, index) => (
                                                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                                                        <Tag className="h-3 w-3" />
                                                        {tag}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Status</Label>
                                        {isEditing ? (
                                            <Select value={watch('status')} onValueChange={(value) => setValue('status', value)}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {statuses.map(status => (
                                                        <SelectItem key={status} value={status}>
                                                            {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <div className="mt-1">
                                                {getStatusBadge(ticket.status)}
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Priority</Label>
                                        {isEditing ? (
                                            <Select value={watch('priority')} onValueChange={(value) => setValue('priority', value)}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {priorities.map(priority => (
                                                        <SelectItem key={priority} value={priority}>
                                                            {priority.charAt(0).toUpperCase() + priority.slice(1)}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <div className="mt-1">
                                                {getPriorityBadge(ticket.priority)}
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Assignee</Label>
                                        {isEditing ? (
                                            <Select value={watch('assigneeId')} onValueChange={(value) => setValue('assigneeId', value)}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {users.map(user => (
                                                        <SelectItem key={user.id} value={user.id}>
                                                            {user.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <div className="mt-1 flex items-center gap-2">
                                                <User className="h-4 w-4 text-gray-500" />
                                                <span>{assignee?.name || 'Unassigned'}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Reporter</Label>
                                        <div className="mt-1 flex items-center gap-2">
                                            <User className="h-4 w-4 text-gray-500" />
                                            <span>{reporter?.name || 'Unknown'}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Created</Label>
                                        <div className="mt-1 flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-gray-500" />
                                            <span className="text-sm">{formatDate(ticket.createdAt)}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Last Updated</Label>
                                        <div className="mt-1 flex items-center gap-2">
                                            <Clock className="h-4 w-4 text-gray-500" />
                                            <span className="text-sm">{formatTimeAgo(ticket.updatedAt)}</span>
                                        </div>
                                    </div>

                                    {ticket.dueDate && (
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Due Date</Label>
                                            <div className="mt-1 flex items-center gap-2">
                                                <Calendar className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm">{formatDate(ticket.dueDate)}</span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="comments" className="space-y-4">
                            <div className="space-y-4">
                                {ticket.comments && ticket.comments.length > 0 ? (
                                    ticket.comments.map((comment) => {
                                        const commentUser = getUserById(comment.userId);
                                        return (
                                            <div key={comment.id} className="border rounded-lg p-4">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <User className="h-4 w-4 text-gray-500" />
                                                    <span className="font-medium">{commentUser?.name || 'Unknown User'}</span>
                                                    <span className="text-sm text-gray-500">
                                                        {formatTimeAgo(comment.createdAt)}
                                                    </span>
                                                </div>
                                                <p className="text-gray-900 whitespace-pre-wrap">{comment.content}</p>
                                            </div>
                                        );
                                    })
                                ) : (
                                    <div className="text-center py-8 text-gray-500">
                                        No comments yet. Be the first to add a comment!
                                    </div>
                                )}

                                <div className="border-t pt-4">
                                    <Label className="text-sm font-medium text-gray-700">Add Comment</Label>
                                    <div className="mt-2 space-y-3">
                                        <textarea
                                            value={newComment}
                                            onChange={(e) => setNewComment(e.target.value)}
                                            placeholder="Write your comment here..."
                                            rows={3}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                        <div className="flex justify-end">
                                            <Button
                                                onClick={handleAddComment}
                                                disabled={!newComment.trim() || isSubmittingComment}
                                                size="sm"
                                            >
                                                <MessageSquare className="h-4 w-4 mr-2" />
                                                {isSubmittingComment ? 'Adding...' : 'Add Comment'}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="activity" className="space-y-4">
                            <div className="text-center py-8 text-gray-500">
                                Activity log coming soon...
                            </div>
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    );
}
