import { createContext, useContext, useState, useEffect } from 'react';
import { authApi } from '../api/apiClient';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
    const [currentUser, setCurrentUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const storedUser = sessionStorage.getItem('user');
        if (storedUser) {
            setCurrentUser(JSON.parse(storedUser));
        }
        setLoading(false);
    }, []);

    const login = async (credentials) => {
        // Handle demo credentials
        if (credentials.username === 'demo' && credentials.password === 'demo') {
            const userData = {
                id: '1',
                username: 'demo',
                name: 'Demo Admin',
                role: 'admin',
                email: '<EMAIL>'
            };

            sessionStorage.setItem('user', JSON.stringify(userData));
            setCurrentUser(userData);
            return { user: userData };
        }

        // Original API-based login for other credentials
        try {
            const response = await authApi.login(credentials);

            if (!response.token) {
                throw new Error('Invalid login response');
            }

            sessionStorage.setItem('token', response.token);

            const userResponse = await authApi.getUser();

            const userData = {
                id: userResponse.id || '1',
                username: userResponse.username || credentials.username,
                name: userResponse.name || `Agent ${credentials.username}`,
                role: userResponse.type || 'agent',
                email: userResponse.email || '',
                auth_username: userResponse.auth_username || '',
                auth_password: userResponse.auth_password || '',
            };

            sessionStorage.setItem('user', JSON.stringify(userData));

            const sipConfig = {
                sipDomain: import.meta.env.VITE_SIP_DOMAIN || 'solutionsv2.tclcontactplus.com',
                name: userData.name,
                authUser: userData.auth_username,
                authPass: userData.auth_password,
                wssPort: import.meta.env.VITE_SIP_PORT || '8089',
            };
            sessionStorage.setItem('sipConfig', JSON.stringify(sipConfig));

            setCurrentUser(userData);

            return { user: userData };
        } catch (error) {
            throw new Error('Invalid credentials');
        }
    };

    const logout = async () => {
        try {
            await authApi.logout();
        } catch (e) {
            console.error('Logout failed', e);
        }
        sessionStorage.clear();
        setCurrentUser(null);
    };

    return (
        <AuthContext.Provider value={{ currentUser, loading, login, logout }}>
            {children}
        </AuthContext.Provider>
    );
};
