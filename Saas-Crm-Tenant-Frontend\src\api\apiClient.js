import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
    withCredentials: true,
});

apiClient.interceptors.request.use(
    (config) => {
        const token = sessionStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
    (response) => response,
    (error) => {
        if (window.location.pathname !== '/login' && error.response?.status === 401) {
            sessionStorage.removeItem('token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);


const authApi = {
    login: async (credentials) => {
        try {
            const response = await apiClient.post('/agent-login', credentials);
            return response.data;
        } catch (error) {
            console.error('Login API error:', error);
            throw error;
        }
    },

    getUser: async () => {
        try {
            const response = await apiClient.get('/get-user');
            return response.data;
        } catch (error) {
            console.error('Get user API error:', error);
            throw error;
        }
    },

    logout: async () => {
        try {
            const response = await apiClient.post('/logout');
            return response.data;
        } catch (error) {
            console.error('Logout API error:', error);
            throw error;
        }
    },
};

const queueApi = {
    getQueues: async () => {
        try {
            const response = await apiClient.get('/agent/get-queue');
            return response.data;
        } catch (error) {
            console.error('Get queues API error:', error);
            throw error;
        }
    },

    login: async (queueIds) => {
        try {
            const response = await apiClient.post('/agent/login', { queues: queueIds });
            return response.data;
        } catch (error) {
            console.error('Queue login API error:', error);
            throw error;
        }
    },

    logout: async () => {
        try {
            const response = await apiClient.post('/agent/logout');
            return response.data;
        } catch (error) {
            console.error('Queue logout API error:', error);
            throw error;
        }
    },

    pause: async (reasonId) => {
        try {
            const response = await apiClient.post('/agent/pause', { reason: reasonId });
            return response.data;
        } catch (error) {
            console.error('Queue pause API error:', error);
            throw error;
        }
    },

    unpause: async () => {
        try {
            const response = await apiClient.post('/agent/unpause');
            return response.data;
        } catch (error) {
            console.error('Queue unpause API error:', error);
            throw error;
        }
    },

    getPauseReasons: async () => {
        try {
            const response = await apiClient.get('/pause-reason');
            return response.data;
        } catch (error) {
            console.error('Get pause reasons API error:', error);
            throw error;
        }
    },

    isReady: async () => {
        try {
            const response = await apiClient.post('/agent/is-ready');
            return response.data;
        } catch (error) {
            console.error('Is ready API error:', error);
            throw error;
        }
    },

    isLogin: async () => {
        try {
            const response = await apiClient.post('/agent/is-login');
            return response.data;
        } catch (error) {
            console.error('Is login API error:', error);
            throw error;
        }
    },
};

export { authApi, queueApi };
export default apiClient;
